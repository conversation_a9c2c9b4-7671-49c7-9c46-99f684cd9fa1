/**
 * Application Host - Main orchestrator
 * Migrated from C# SideView.Core.AppHost
 */

// Removed unused imports: B<PERSON>erWindow, screen
import { ServiceContainer } from '@modules/core/service-container';
import { AppEventBus } from '@modules/core/event-bus';
import { ConfigurationService } from '@modules/core/configuration-service';
import { UIService } from '@modules/ui/ui-service';
import { WebEngineService } from '@modules/webengine/webengine-service';
import { AppManagerService } from '@modules/appmanager/appmanager-service';
import { SessionManagerService } from '@modules/session/session-service';
import { NotificationService } from '@modules/notifications/notification-service';
import { HotkeyService } from '@modules/hotkeys/hotkey-service';
import { UpdaterService } from '@modules/updater/updater-service';
import { TabManagerService } from '@modules/tabs/tab-manager-service';
import { CreateAppRequest, UpdateAppRequest, WebAppModel, AppSettings } from '@shared/types/app.types';
import { CoreModuleStartedEvent, CoreModuleStoppedEvent } from '@shared/types/events.types';

export class AppHost {
  private readonly serviceContainer: ServiceContainer;
  private readonly logger: Console;
  private isStarted = false;
  private isDisposed = false;

  // Service references
  private eventBus!: AppEventBus;
  private configurationService!: ConfigurationService;
  private uiService!: UIService;
  private webEngineService!: WebEngineService;
  private appManagerService!: AppManagerService;
  private sessionManagerService!: SessionManagerService;
  private notificationService!: NotificationService;
  private hotkeyService!: HotkeyService;
  private updaterService!: UpdaterService;
  private tabManagerService!: TabManagerService;

  constructor() {
    this.logger = console;
    this.serviceContainer = new ServiceContainer(this.logger);
    this.configureServices();
  }

  /**
   * Starts the application host and all modules
   */
  async startAsync(): Promise<void> {
    if (this.isStarted) {
      throw new Error('AppHost is already started');
    }

    try {
      this.logger.info('Starting SideView application host');

      // Get service instances
      this.getServiceInstances();

      // Initialize modules in dependency order
      await this.initializeModules();

      // Start modules
      await this.startModules();

      this.isStarted = true;
      await this.eventBus.publish(new CoreModuleStartedEvent());

      this.logger.info('SideView application host started successfully');

    } catch (error) {
      this.logger.error('Failed to start SideView application host:', error);
      throw error;
    }
  }

  /**
   * Stops the application host and all modules
   */
  async stopAsync(): Promise<void> {
    if (!this.isStarted) {
      return;
    }

    try {
      this.logger.info('Stopping SideView application host');

      // Stop modules in reverse order
      await this.stopModules();

      this.isStarted = false;
      await this.eventBus.publish(new CoreModuleStoppedEvent());

      this.logger.info('SideView application host stopped successfully');

    } catch (error) {
      this.logger.error('Error stopping SideView application host:', error);
      throw error;
    }
  }

  // Public API methods for IPC handlers

  async showPanel(): Promise<void> {
    return this.uiService.showPanel(true);
  }

  async hidePanel(): Promise<void> {
    return this.uiService.hidePanel(true);
  }

  async togglePanel(): Promise<void> {
    return this.uiService.togglePanel();
  }

  async getApps(): Promise<WebAppModel[]> {
    return this.appManagerService.getAllApps();
  }

  async createApp(request: CreateAppRequest): Promise<WebAppModel> {
    const app = await this.appManagerService.createApp(request);

    // Create WebAppHost for the new app
    await this.webEngineService.createWebAppHost(app);

    return app;
  }

  async updateApp(request: UpdateAppRequest): Promise<WebAppModel> {
    return this.appManagerService.updateApp(request);
  }

  async deleteApp(appId: string): Promise<void> {
    // Remove WebAppHost first
    await this.webEngineService.removeWebAppHost(appId);

    // Then delete from app manager
    await this.appManagerService.deleteApp(appId);
  }

  async activateApp(appId: string): Promise<void> {
    return this.webEngineService.switchToWebApp(appId);
  }

  getConfiguration(): AppSettings {
    return this.configurationService.getSettings();
  }

  async updateConfiguration(updates: Partial<AppSettings>): Promise<void> {
    return this.configurationService.updateSettings(updates);
  }

  // Theme management methods
  async getTheme(): Promise<{ theme: string; isDark: boolean; variables: Record<string, string> } | undefined> {
    if (this.uiService) {
      return this.uiService.getTheme();
    }
    return undefined;
  }

  async setTheme(theme: string): Promise<void> {
    if (this.uiService) {
      await this.uiService.setTheme(theme);
    }
  }

  async getThemeVariables(): Promise<Record<string, string> | undefined> {
    if (this.uiService) {
      return this.uiService.getThemeVariables();
    }
    return undefined;
  }

  // UI state methods
  async setPinned(pinned: boolean): Promise<void> {
    if (this.uiService) {
      await this.uiService.setPinned(pinned);
    }
  }

  async isVisible(): Promise<boolean> {
    if (this.uiService) {
      return this.uiService.getIsVisible();
    }
    return false;
  }

  async isPinned(): Promise<boolean> {
    if (this.uiService) {
      return this.uiService.getIsPinned();
    }
    return false;
  }

  // Tab management methods
  async getTabs(): Promise<any[]> {
    return this.tabManagerService.getAllTabs();
  }

  async getActiveTab(): Promise<any> {
    return this.tabManagerService.getActiveTab();
  }

  async createTab(request: any): Promise<any> {
    return this.tabManagerService.createTab(request);
  }

  async closeTab(tabId: string): Promise<void> {
    return this.tabManagerService.closeTab(tabId);
  }

  async activateTab(tabId: string): Promise<void> {
    return this.tabManagerService.activateTab(tabId);
  }

  async navigateTab(tabId: string, url: string): Promise<void> {
    return this.tabManagerService.navigateTab(tabId, url);
  }

  async reloadTab(tabId: string): Promise<void> {
    return this.tabManagerService.reloadTab(tabId);
  }

  async getTabManagerState(): Promise<any> {
    return this.tabManagerService.getTabManagerState();
  }

  private configureServices(): void {
    // Core services
    this.serviceContainer.addSingleton('EventBus', AppEventBus);
    this.serviceContainer.addSingleton('ConfigurationService', ConfigurationService);

    // Module services with proper dependency injection
    this.serviceContainer.addSingleton('SessionManagerService', SessionManagerService, (container) => {
      return new SessionManagerService(
        container.get('EventBus'),
        this.logger
      );
    });

    this.serviceContainer.addSingleton('AppManagerService', AppManagerService, (container) => {
      return new AppManagerService(
        container.get('EventBus'),
        this.logger
      );
    });

    this.serviceContainer.addSingleton('WebEngineService', WebEngineService, (container) => {
      return new WebEngineService(
        container.get('EventBus'),
        container.get('ConfigurationService'),
        container.get('SessionManagerService'),
        this.logger
      );
    });

    this.serviceContainer.addSingleton('UIService', UIService, (container) => {
      return new UIService(
        container.get('EventBus'),
        container.get('ConfigurationService'),
        this.logger
      );
    });

    this.serviceContainer.addSingleton('NotificationService', NotificationService, (container) => {
      return new NotificationService(
        container.get('EventBus'),
        this.logger
      );
    });

    this.serviceContainer.addSingleton('HotkeyService', HotkeyService, (container) => {
      return new HotkeyService(
        container.get('EventBus'),
        this.logger
      );
    });

    this.serviceContainer.addSingleton('UpdaterService', UpdaterService, (container) => {
      return new UpdaterService(
        container.get('EventBus'),
        this.logger
      );
    });

    this.serviceContainer.addSingleton('TabManagerService', TabManagerService, (container) => {
      return new TabManagerService(
        container.get('EventBus'),
        container.get('WebEngineService'),
        container.get('AppManagerService'),
        this.logger
      );
    });

    this.logger.debug('Services configured successfully');
  }

  private getServiceInstances(): void {
    this.eventBus = this.serviceContainer.get<AppEventBus>('EventBus');
    this.configurationService = this.serviceContainer.get<ConfigurationService>('ConfigurationService');
    this.uiService = this.serviceContainer.get<UIService>('UIService');
    this.webEngineService = this.serviceContainer.get<WebEngineService>('WebEngineService');
    this.appManagerService = this.serviceContainer.get<AppManagerService>('AppManagerService');
    this.sessionManagerService = this.serviceContainer.get<SessionManagerService>('SessionManagerService');
    this.notificationService = this.serviceContainer.get<NotificationService>('NotificationService');
    this.hotkeyService = this.serviceContainer.get<HotkeyService>('HotkeyService');
    this.updaterService = this.serviceContainer.get<UpdaterService>('UpdaterService');
    this.tabManagerService = this.serviceContainer.get<TabManagerService>('TabManagerService');

    this.logger.debug('Service instances retrieved successfully');
  }

  private async initializeModules(): Promise<void> {
    this.logger.info('Initializing modules...');

    // Initialize in dependency order
    await this.configurationService.getSettings(); // Ensure config is loaded
    await this.sessionManagerService.initialize();
    await this.appManagerService.initialize();
    await this.webEngineService.initialize();
    await this.uiService.initialize();

    // Connect UI and WebEngine services
    this.uiService.setWebEngineService(this.webEngineService);

    await this.notificationService.initialize();
    await this.hotkeyService.initialize();
    await this.updaterService.initialize();
    await this.tabManagerService.initialize();

    this.logger.info('All modules initialized successfully');
  }

  private async startModules(): Promise<void> {
    // Start in dependency order
    await this.sessionManagerService.start();
    await this.appManagerService.start();
    await this.webEngineService.start();

    // Create WebAppHosts for existing apps
    await this.initializeExistingApps();

    await this.uiService.start();
    await this.notificationService.start();
    await this.hotkeyService.start();
    await this.updaterService.start();
    await this.tabManagerService.start();
  }

  private async initializeExistingApps(): Promise<void> {
    try {
      const apps = await this.appManagerService.getAllApps();

      for (const app of apps) {
        try {
          await this.webEngineService.createWebAppHost(app);
        } catch (error) {
          this.logger.error(`Failed to create WebAppHost for app ${app.name}:`, error);
        }
      }

    } catch (error) {
      this.logger.error('Failed to initialize existing apps:', error);
      // Don't throw - this shouldn't prevent startup
    }
  }

  private async stopModules(): Promise<void> {

    // Stop in reverse order
    try { await this.tabManagerService.stop(); } catch (e) { this.logger.error('Error stopping tab manager:', e); }
    try { await this.updaterService.stop(); } catch (e) { this.logger.error('Error stopping updater:', e); }
    try { await this.hotkeyService.stop(); } catch (e) { this.logger.error('Error stopping hotkeys:', e); }
    try { await this.notificationService.stop(); } catch (e) { this.logger.error('Error stopping notifications:', e); }
    try { await this.uiService.stop(); } catch (e) { this.logger.error('Error stopping UI:', e); }
    try { await this.webEngineService.stop(); } catch (e) { this.logger.error('Error stopping web engine:', e); }
    try { await this.appManagerService.stop(); } catch (e) { this.logger.error('Error stopping app manager:', e); }
    try { await this.sessionManagerService.stop(); } catch (e) { this.logger.error('Error stopping session manager:', e); }

    this.logger.info('All modules stopped');
  }

  dispose(): void {
    if (this.isDisposed) {
      return;
    }

    this.isDisposed = true;
    this.serviceContainer.clear();
    this.logger.info('AppHost disposed');
  }
}
